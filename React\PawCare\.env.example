# PawCare React App Environment Variables
# Copy this file to .env and fill in your actual values

# Stripe Configuration
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key_here

# Backend API Configuration
REACT_APP_API_BASE_URL=http://localhost:3000/api/v0

# Development Settings
REACT_APP_ENVIRONMENT=development

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace the placeholder values with your actual Stripe keys
# 3. Make sure your backend is configured with the corresponding secret key
# 4. Never commit your .env file to version control
