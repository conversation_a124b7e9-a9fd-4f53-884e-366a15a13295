# Stripe Payment Integration Setup Guide

## Overview
This guide explains how to complete the Stripe payment integration for your PawCare application.

## Frontend Setup (Already Completed)

### Files Created:
1. `src/config/stripe.js` - Stripe configuration
2. `src/services/paymentService.js` - API service for payment calls
3. `src/components/Payment/StripeProvider.jsx` - Stripe Elements provider
4. `src/components/Payment/CheckoutForm.jsx` - Payment form component
5. `src/components/Payment/CheckoutPage.jsx` - Complete checkout page
6. `src/components/Payment/PaymentSuccess.jsx` - Success page
7. `src/components/Payment/PaymentFailure.jsx` - Failure page

### Files Modified:
1. `src/App.jsx` - Added payment routes
2. `src/Components/Cart/Cart.jsx` - Updated checkout button
3. `src/Components/Url/EndUrl.js` - Added payment endpoints
4. `src/Components/Url/BaseUrl.js` - Added payment base URL

## Required Setup Steps

### 1. Get Stripe Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your **Publishable Key** and **Secret Key**
3. Update `src/config/stripe.js` with your publishable key:
   ```javascript
   const STRIPE_PUBLISHABLE_KEY = 'pk_test_your_actual_publishable_key_here';
   ```

### 2. Backend Setup
1. Make sure your backend has the Stripe package installed:
   ```bash
   npm install stripe
   ```

2. Create a `config/stripe.js` file in your backend:
   ```javascript
   const stripe = require('stripe')('sk_test_your_secret_key_here');
   module.exports = stripe;
   ```

3. Fix your payment controller (the code you provided had a missing router import):
   ```javascript
   // At the top of your paymentController.js
   const express = require('express');
   const router = express.Router();
   
   // ... your existing controller code ...
   
   // At the bottom, export the router
   module.exports = router;
   ```

4. Set up your payment routes in your main server file:
   ```javascript
   const paymentRoutes = require('./routes/paymentRoutes'); // or wherever your routes are
   app.use('/api/payment', paymentRoutes);
   ```

### 3. Environment Variables
Create a `.env` file in your backend with:
```
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

### 4. CORS Configuration
Make sure your backend allows requests from your React app:
```javascript
app.use(cors({
  origin: 'http://localhost:5173', // or your React app URL
  credentials: true
}));
```

## Testing the Integration

### 1. Start Your Backend
Make sure your backend server is running on `http://localhost:3000`

### 2. Start Your Frontend
```bash
npm run dev
```

### 3. Test Payment Flow
1. Add items to cart
2. Go to cart page
3. Click "Proceed to Checkout"
4. Fill in payment details using Stripe test cards:
   - **Success**: 4242 4242 4242 4242
   - **Decline**: 4000 0000 0000 0002
   - Use any future expiry date and any 3-digit CVC

### 4. Test Cards
- **Visa**: 4242 4242 4242 4242
- **Visa (debit)**: 4000 0566 5566 5556
- **Mastercard**: 5555 5555 5555 4444
- **American Express**: 3782 822463 10005

## Troubleshooting

### Common Issues:
1. **"Stripe key not found"** - Update your publishable key in `src/config/stripe.js`
2. **CORS errors** - Check your backend CORS configuration
3. **Payment intent creation fails** - Verify your backend routes and secret key
4. **404 on payment routes** - Check that your backend routes are properly mounted

### Debug Steps:
1. Check browser console for errors
2. Check backend logs for API call errors
3. Verify Stripe dashboard for payment attempts
4. Test with Stripe's test cards

## Security Notes
- Never expose your secret key in frontend code
- Always validate payments on the backend
- Use HTTPS in production
- Implement proper error handling

## Next Steps
1. Add order management system
2. Implement email confirmations
3. Add payment history page
4. Set up webhooks for payment status updates
5. Add refund functionality

## Support
If you encounter issues:
1. Check Stripe documentation: https://stripe.com/docs
2. Review browser console errors
3. Check backend server logs
4. Test with different payment methods
