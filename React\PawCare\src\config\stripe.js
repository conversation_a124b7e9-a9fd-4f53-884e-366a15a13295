import { loadStripe } from '@stripe/stripe-js';

// Replace with your actual Stripe publishable key
// You should get this from your Stripe dashboard
const STRIPE_PUBLISHABLE_KEY = 'pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4';

// Initialize Stripe
let stripePromise;

const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

export default getStripe;

// You can also export the key if needed elsewhere
export { STRIPE_PUBLISHABLE_KEY };
