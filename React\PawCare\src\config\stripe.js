import { loadStripe } from '@stripe/stripe-js';

// Stripe publishable key from environment variables or fallback
// Add REACT_APP_STRIPE_PUBLISHABLE_KEY to your .env file
const STRIPE_PUBLISHABLE_KEY =
  process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY ||
  'pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4';

// Validate that the key is provided and valid
if (!STRIPE_PUBLISHABLE_KEY || !STRIPE_PUBLISHABLE_KEY.startsWith('pk_')) {
  console.error('⚠️ STRIPE CONFIGURATION ERROR: Invalid Stripe publishable key');
  console.error('Please set REACT_APP_STRIPE_PUBLISHABLE_KEY in your .env file');
}

// Initialize Stripe
let stripePromise;

const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

export default getStripe;

// You can also export the key if needed elsewhere
export { STRIPE_PUBLISHABLE_KEY };
