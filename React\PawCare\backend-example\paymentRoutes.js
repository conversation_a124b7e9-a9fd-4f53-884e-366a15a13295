// Example backend payment routes file
// This shows how to properly set up your backend payment routes

const express = require('express');
const router = express.Router();

// Import your payment controller (the code you provided)
const { 
  createPaymentIntent, 
  confirmPayment, 
  createCustomer, 
  getPaymentHistory 
} = require('../controller/paymentController');

// Payment routes
router.post('/create-payment-intent', createPaymentIntent);
router.post('/confirm-payment', confirmPayment);
router.post('/create-customer', createCustomer);
router.get('/payment-history', getPaymentHistory);

module.exports = router;

// In your main app.js or server.js file, you would use this like:
// const paymentRoutes = require('./routes/paymentRoutes');
// app.use('/api/payment', paymentRoutes);

// Make sure your backend server is running on http://localhost:3000
// and that you have the correct CORS settings to allow requests from your React app
