import React, { useState, useEffect } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useCart } from '../../context/CartContext';
import { paymentService } from '../../services/paymentService';
import { FaLock, FaSpinner } from 'react-icons/fa';

const CheckoutForm = ({ onSuccess, onError }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { cartTotal, cartItems, clearCart } = useCart();

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [clientSecret, setClientSecret] = useState('');

  // Create payment intent when component mounts
  useEffect(() => {
    if (cartTotal > 0) {
      createPaymentIntent();
    }
  }, [cartTotal]);

  const createPaymentIntent = async () => {
    try {
      setIsLoading(true);

      // Convert cart total to cents (<PERSON><PERSON> expects amounts in cents)
      const amount = Math.round(cartTotal * 100);

      const paymentData = {
        amount: amount,
        currency: 'usd',
        description: `PawCare Order - ${cartItems.length} items`
      };

      const response = await paymentService.createPaymentIntent(paymentData);

      if (response.success) {
        setClientSecret(response.data.clientSecret);

        // If this is a mock response (backend not configured), show a warning
        if (response.data.clientSecret === 'pi_mock_client_secret_for_testing') {
          setMessage('⚠️ Demo mode: Backend payment routes not configured. This is for UI testing only.');
        }
      } else {
        setMessage(response.message || 'Failed to initialize payment');
        onError && onError(response.message);
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      setMessage('Failed to initialize payment. Please try again.');
      onError && onError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      // Check if this is a mock payment (backend not configured)
      if (clientSecret === 'pi_mock_client_secret_for_testing') {
        // Simulate payment processing for demo
        setTimeout(() => {
          setMessage('✅ Demo payment successful! (No real payment processed)');
          clearCart();
          const mockPaymentIntent = {
            id: 'pi_mock_' + Date.now(),
            status: 'succeeded',
            amount: Math.round(cartTotal * 100),
            currency: 'usd'
          };
          onSuccess && onSuccess(mockPaymentIntent);
          setIsLoading(false);
        }, 2000);
        return;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: 'if_required'
      });

      if (error) {
        setMessage(error.message);
        onError && onError(error);
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment successful
        setMessage('Payment successful!');
        clearCart(); // Clear the cart after successful payment
        onSuccess && onSuccess(paymentIntent);
      }
    } catch (error) {
      console.error('Payment error:', error);
      setMessage('An unexpected error occurred.');
      onError && onError(error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!clientSecret) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-2xl text-[#575CEE]" />
        <span className="ml-2">Initializing payment...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">Order Summary</h3>
        <div className="flex justify-between items-center">
          <span>Total ({cartItems.length} items)</span>
          <span className="font-bold text-[#575CEE]">${cartTotal.toFixed(2)}</span>
        </div>
      </div>

      <div className="space-y-4">
        {clientSecret === 'pi_mock_client_secret_for_testing' ? (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <div className="text-gray-500 mb-4">
              <FaLock className="text-4xl mx-auto mb-2" />
              <h3 className="font-semibold">Demo Payment Form</h3>
              <p className="text-sm">Backend not configured - UI testing mode</p>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded p-3 text-sm text-yellow-800">
              This is a demo. No real payment will be processed.
              <br />
              Click "Pay" below to simulate a successful payment.
            </div>
          </div>
        ) : (
          <PaymentElement
            id="payment-element"
            options={{
              layout: "tabs"
            }}
          />
        )}
      </div>

      {message && (
        <div className={`p-3 rounded-md text-sm ${
          message.includes('successful')
            ? 'bg-green-100 text-green-700'
            : 'bg-red-100 text-red-700'
        }`}>
          {message}
        </div>
      )}

      <button
        disabled={isLoading || !stripe || !elements}
        type="submit"
        className="w-full bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
      >
        {isLoading ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Processing...
          </>
        ) : (
          <>
            <FaLock className="mr-2" />
            Pay ${cartTotal.toFixed(2)}
          </>
        )}
      </button>

      <div className="text-center text-sm text-gray-500">
        <FaLock className="inline mr-1" />
        Your payment information is secure and encrypted
      </div>
    </form>
  );
};

export default CheckoutForm;
