const stripe = require('../config/stripe'); 

// Create Payment Intent 
exports.createPaymentIntent = async (req, res) => { 
    try { 
        const { amount, currency = 'usd', description } = req.body; 

        // Validate amount 
        if (!amount || amount < 50) { 
            return res.status(400).json({ 
                success: false, 
                message: 'Amount must be at least $0.50' 
            }); 
        } 

        // Create payment intent 
        const paymentIntent = await stripe.paymentIntents.create({ 
            amount: amount, // Amount in cents (e.g., 2000 = $20.00) 
            currency: currency, 
            description: description || 'Payment for services', 
            automatic_payment_methods: { 
                enabled: true, 
            }, 
        }); 

        res.status(200).json({ 
            success: true, 
            message: 'Payment intent created successfully', 
            data: { 
                clientSecret: paymentIntent.client_secret, 
                paymentIntentId: paymentIntent.id, 
                amount: paymentIntent.amount, 
                currency: paymentIntent.currency 
            } 
        }); 

    } catch (error) { 
        console.error('Payment Intent Error:', error); 
        res.status(500).json({ 
            success: false, 
            message: 'Failed to create payment intent', 
            error: error.message 
        }); 
    } 
}; 

// Confirm Payment 
exports.confirmPayment = async (req, res) => { 
    try { 
        const { paymentIntentId } = req.body; 

        if (!paymentIntentId) { 
            return res.status(400).json({ 
                success: false, 
                message: 'Payment Intent ID is required' 
            }); 
        } 

        // Retrieve payment intent 
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId); 

        res.status(200).json({ 
            success: true, 
            message: 'Payment status retrieved', 
            data: { 
                id: paymentIntent.id, 
                status: paymentIntent.status, 
                amount: paymentIntent.amount, 
                currency: paymentIntent.currency, 
                created: paymentIntent.created 
            } 
        }); 

    } catch (error) { 
        console.error('Payment Confirmation Error:', error); 
        res.status(500).json({ 
            success: false, 
            message: 'Failed to confirm payment', 
            error: error.message 
        }); 
    } 
}; 

// Create Customer 
exports.createCustomer = async (req, res) => { 
    try { 
        const { email, name, phone } = req.body; 

        if (!email) { 
            return res.status(400).json({ 
                success: false, 
                message: 'Email is required' 
            }); 
        } 

        const customer = await stripe.customers.create({ 
            email: email, 
            name: name, 
            phone: phone 
        }); 

        res.status(200).json({ 
            success: true, 
            message: 'Customer created successfully', 
            data: { 
                customerId: customer.id, 
                email: customer.email, 
                name: customer.name 
            } 
        }); 

    } catch (error) { 
        console.error('Customer Creation Error:', error); 
        res.status(500).json({ 
            success: false, 
            message: 'Failed to create customer', 
            error: error.message 
        }); 
    } 
}; 

// Get Payment History 
exports.getPaymentHistory = async (req, res) => { 
    try { 
        const { limit = 10 } = req.query; 

        const paymentIntents = await stripe.paymentIntents.list({ 
            limit: parseInt(limit) 
        }); 

        res.status(200).json({ 
            success: true, 
            message: 'Payment history retrieved', 
            data: paymentIntents.data.map(payment => ({ 
                id: payment.id, 
                amount: payment.amount, 
                currency: payment.currency, 
                status: payment.status, 
                created: payment.created, 
                description: payment.description 
            })) 
        }); 

    } catch (error) { 
        console.error('Payment History Error:', error); 
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get payment history', 
            error: error.message 
        }); 
    } 
};
