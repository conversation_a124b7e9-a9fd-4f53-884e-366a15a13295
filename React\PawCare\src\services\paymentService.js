import axios from 'axios';
import { Payment_BaseUrl } from '../Components/Url/BaseUrl';
import {
  Add_PaymentIntentUrl,
  Add_ConfirmPaymentUrl,
  Add_CreateCustomerUrl,
  Add_PaymentHistoryUrl
} from '../Components/Url/EndUrl';

// Create axios instance with base configuration for payment API
const paymentAPI = axios.create({
  baseURL: Payment_BaseUrl, // http://localhost:3000/api/payment
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout for payment operations
});

// Add request interceptor to include auth token if available
paymentAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('isAuthenticated');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Payment Service Functions
export const paymentService = {
  // Create Payment Intent
  createPaymentIntent: async (paymentData) => {
    try {
      const response = await paymentAPI.post(Add_PaymentIntentUrl, paymentData);
      return response.data;
    } catch (error) {
      console.error('Error creating payment intent:', error);

      // Provide detailed error information for debugging
      if (error.response?.status === 404) {
        throw new Error('Payment endpoint not found. Please ensure your backend payment routes are configured at /api/payment/create-payment-intent');
      }

      if (error.response?.status === 500) {
        throw new Error('Server error occurred while creating payment intent. Please check your backend configuration.');
      }

      if (!error.response) {
        throw new Error('Unable to connect to payment server. Please check if your backend is running.');
      }

      // Return the actual error from backend
      throw error.response?.data || new Error(error.message || 'Payment initialization failed');
    }
  },

  // Confirm Payment
  confirmPayment: async (paymentIntentId) => {
    try {
      const response = await paymentAPI.post(Add_ConfirmPaymentUrl, {
        paymentIntentId
      });
      return response.data;
    } catch (error) {
      console.error('Error confirming payment:', error);

      if (error.response?.status === 404) {
        throw new Error('Payment confirmation endpoint not found. Please ensure your backend payment routes are configured.');
      }

      if (error.response?.status === 400) {
        throw new Error('Invalid payment intent ID provided.');
      }

      throw error.response?.data || new Error(error.message || 'Payment confirmation failed');
    }
  },

  // Create Customer
  createCustomer: async (customerData) => {
    try {
      const response = await paymentAPI.post(Add_CreateCustomerUrl, customerData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error.response?.data || error.message;
    }
  },

  // Get Payment History
  getPaymentHistory: async (limit = 10) => {
    try {
      const response = await paymentAPI.get(`${Add_PaymentHistoryUrl}?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error.response?.data || error.message;
    }
  }
};

export default paymentService;
