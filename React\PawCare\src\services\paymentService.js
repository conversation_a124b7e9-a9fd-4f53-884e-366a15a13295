import axios from 'axios';
import { Payment_BaseUrl } from '../Components/Url/BaseUrl';
import {
  Add_PaymentIntentUrl,
  Add_ConfirmPaymentUrl,
  Add_CreateCustomerUrl,
  Add_PaymentHistoryUrl
} from '../Components/Url/EndUrl';

// Create axios instance with base configuration
const paymentAPI = axios.create({
  baseURL: Payment_BaseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token if available
paymentAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('isAuthenticated');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Payment Service Functions
export const paymentService = {
  // Create Payment Intent
  createPaymentIntent: async (paymentData) => {
    try {
      const response = await paymentAPI.post(Add_PaymentIntentUrl, paymentData);
      return response.data;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error.response?.data || error.message;
    }
  },

  // Confirm Payment
  confirmPayment: async (paymentIntentId) => {
    try {
      const response = await paymentAPI.post(Add_ConfirmPaymentUrl, {
        paymentIntentId
      });
      return response.data;
    } catch (error) {
      console.error('Error confirming payment:', error);
      throw error.response?.data || error.message;
    }
  },

  // Create Customer
  createCustomer: async (customerData) => {
    try {
      const response = await paymentAPI.post(Add_CreateCustomerUrl, customerData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error.response?.data || error.message;
    }
  },

  // Get Payment History
  getPaymentHistory: async (limit = 10) => {
    try {
      const response = await paymentAPI.get(`${Add_PaymentHistoryUrl}?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error.response?.data || error.message;
    }
  }
};

export default paymentService;
