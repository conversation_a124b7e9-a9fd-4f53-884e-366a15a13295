// Backend Connection Test Script
// Run this to verify your backend payment routes are working

const axios = require('axios');

const PAYMENT_BASE_URL = 'http://localhost:3000/api/payment';

async function testBackendConnection() {
  console.log('🔍 Testing Backend Payment Routes...\n');

  // Test 1: Check if server is running
  try {
    console.log('1. Testing server connection...');
    const response = await axios.get('http://localhost:3000');
    console.log('✅ Server is running');
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('   Please start your backend server first');
    return;
  }

  // Test 2: Check payment intent endpoint
  try {
    console.log('\n2. Testing payment intent endpoint...');
    const response = await axios.post(`${PAYMENT_BASE_URL}/create-payment-intent`, {
      amount: 2000, // $20.00 in cents
      currency: 'usd',
      description: 'Test payment'
    });
    
    if (response.data.success && response.data.data.clientSecret) {
      console.log('✅ Payment intent endpoint working');
      console.log(`   Client secret received: ${response.data.data.clientSecret.substring(0, 20)}...`);
    } else {
      console.log('❌ Payment intent endpoint returned unexpected response');
      console.log('   Response:', response.data);
    }
  } catch (error) {
    console.log('❌ Payment intent endpoint failed');
    if (error.response?.status === 404) {
      console.log('   Route not found - check if /api/payment/create-payment-intent is configured');
    } else if (error.response?.status === 500) {
      console.log('   Server error - check your Stripe secret key configuration');
      console.log('   Error:', error.response?.data);
    } else {
      console.log('   Error:', error.message);
    }
  }

  // Test 3: Check confirm payment endpoint
  try {
    console.log('\n3. Testing confirm payment endpoint...');
    const response = await axios.post(`${PAYMENT_BASE_URL}/confirm-payment`, {
      paymentIntentId: 'pi_test_123'
    });
    console.log('✅ Confirm payment endpoint accessible');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Confirm payment endpoint working (expected 400 for test ID)');
    } else if (error.response?.status === 404) {
      console.log('❌ Confirm payment route not found');
    } else {
      console.log('❌ Confirm payment endpoint error:', error.message);
    }
  }

  console.log('\n🎯 Backend Test Complete!');
  console.log('\nIf you see errors above, please:');
  console.log('1. Make sure your backend server is running on port 3000');
  console.log('2. Verify payment routes are configured at /api/payment/*');
  console.log('3. Check your Stripe secret key is properly set');
  console.log('4. Ensure CORS is configured to allow requests from your React app');
}

// Run the test
testBackendConnection().catch(console.error);
